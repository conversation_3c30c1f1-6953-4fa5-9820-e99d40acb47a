#[cfg(test)]
mod integration_tests {
    use super::*;
    use crate::ingredient_parsing::IngredientParser;
    use std::time::{Duration, Instant};
    use tokio_test;

    /// Integration test with real Kalosm model (if available)
    #[tokio::test]
    async fn test_real_kalosm_integration() {
        let parser = IngredientParser::new();
        
        // Test ingredients with known expected results
        let test_cases = vec![
            ("2 cups all-purpose flour", "flour", Some(2.0), Some("cups")),
            ("1 tablespoon olive oil", "olive oil", Some(1.0), Some("tablespoon")),
            ("3 large eggs", "eggs", Some(3.0), None),
            ("1/2 cup sugar", "sugar", Some(0.5), Some("cup")),
            ("1 teaspoon vanilla extract", "vanilla extract", Some(1.0), Some("teaspoon")),
        ];

        let mut successful_parses = 0;
        let mut total_time = Duration::new(0, 0);

        for (ingredient_text, expected_name, expected_amount, expected_unit) in test_cases {
            let start_time = Instant::now();
            
            match parser.parse_ingredient(ingredient_text, None).await {
                Ok(Some(ingredient)) => {
                    successful_parses += 1;
                    total_time += start_time.elapsed();
                    
                    // Validate parsing accuracy
                    assert!(ingredient.name.to_lowercase().contains(&expected_name.to_lowercase()),
                        "Expected ingredient name '{}' to contain '{}', got '{}'", 
                        ingredient.name, expected_name, ingredient.name);
                    
                    if let Some(expected_amt) = expected_amount {
                        assert!(!ingredient.amount.is_empty(),
                            "Expected amount {} for '{}', got empty string", expected_amt, ingredient_text);
                        let actual_amount: f64 = ingredient.amount.parse().unwrap_or(0.0);
                        assert!((actual_amount - expected_amt).abs() < 0.1,
                            "Expected amount {} for '{}', got {}", expected_amt, ingredient_text, actual_amount);
                    }
                    
                    if let Some(expected_u) = expected_unit {
                        assert!(!ingredient.unit.is_empty(),
                            "Expected unit '{}' for '{}', got empty string", expected_u, ingredient_text);
                        let actual_unit = &ingredient.unit;
                        assert!(actual_unit.to_lowercase().contains(&expected_u.to_lowercase()),
                            "Expected unit '{}' for '{}', got '{}'", expected_u, ingredient_text, actual_unit);
                    }
                    
                    println!("✓ Successfully parsed: '{}' -> {:?}", ingredient_text, ingredient);
                }
                Ok(None) => {
                    println!("⚠ Kalosm returned None for: '{}'", ingredient_text);
                }
                Err(e) => {
                    println!("✗ Failed to parse '{}': {}", ingredient_text, e);
                }
            }
        }

        let avg_time = total_time / successful_parses.max(1) as u32;
        println!("Integration test results:");
        println!("  Successful parses: {}/5", successful_parses);
        println!("  Average parse time: {}ms", avg_time.as_millis());
        
        // Should have at least 60% success rate for integration test
        assert!(successful_parses >= 3, 
            "Integration test failed: only {}/5 ingredients parsed successfully", successful_parses);
    }

    /// Test error scenarios and fallback behavior
    #[tokio::test]
    async fn test_error_scenarios_and_fallback() {
        let parser = IngredientParser::new();
        
        let long_string = "a".repeat(1000);
        let error_cases = vec![
            "", // Empty string
            "   ", // Whitespace only
            "invalid ingredient text that should fail parsing", // Invalid format
            "🍕🍔🍟", // Emoji only
            &long_string, // Very long string
        ];

        for error_case in error_cases {
            match parser.parse_ingredient(error_case, None).await {
                Ok(None) => {
                    println!("✓ Correctly handled invalid input: '{}'", 
                        if error_case.len() > 50 { &error_case[..50] } else { error_case });
                }
                Ok(Some(ingredient)) => {
                    println!("⚠ Unexpectedly parsed invalid input '{}' as: {:?}", 
                        if error_case.len() > 50 { &error_case[..50] } else { error_case }, ingredient);
                }
                Err(e) => {
                    println!("✓ Error handling worked for '{}': {}", 
                        if error_case.len() > 50 { &error_case[..50] } else { error_case }, e);
                }
            }
        }
    }

    /// Test batch parsing performance under load
    #[tokio::test]
    async fn test_batch_parsing_under_load() {
        let parser = IngredientParser::new();
        
        // Create a large batch of ingredients
        let base_ingredients = vec![
            "2 cups flour",
            "1 tbsp oil", 
            "3 eggs",
            "1/2 cup sugar",
            "1 tsp vanilla",
            "1 cup milk",
            "2 tsp baking powder",
            "1/2 tsp salt",
            "1/4 cup butter",
            "1 tsp cinnamon",
        ];

        // Create 50 ingredients (5 batches of 10)
        let mut large_batch = Vec::new();
        for i in 0..5 {
            for ingredient in &base_ingredients {
                large_batch.push(format!("{} (batch {})", ingredient, i + 1));
            }
        }

        let start_time = Instant::now();
        let results = parser.parse_ingredients_batch(&large_batch).await;
        let elapsed = start_time.elapsed();

        match results {
            Ok(parsed) => {
                let success_rate = parsed.len() as f64 / large_batch.len() as f64;
                let avg_time_per_ingredient = elapsed.as_millis() as f64 / large_batch.len() as f64;

                println!("Batch parsing under load results:");
                println!("  Total ingredients: {}", large_batch.len());
                println!("  Successfully parsed: {}", parsed.len());
                println!("  Success rate: {:.1}%", success_rate * 100.0);
                println!("  Total time: {}ms", elapsed.as_millis());
                println!("  Average per ingredient: {:.2}ms", avg_time_per_ingredient);

                // Performance targets from KALOSM-INTEGRATION.md
                assert!(avg_time_per_ingredient < 100.0, 
                    "Batch parsing too slow: {:.2}ms per ingredient", avg_time_per_ingredient);
                assert!(success_rate > 0.5, 
                    "Batch parsing success rate too low: {:.1}%", success_rate * 100.0);
            }
            Err(e) => {
                panic!("Batch parsing failed: {}", e);
            }
        }
    }

    /// Test parsing accuracy against known dataset
    #[tokio::test]
    async fn test_parsing_accuracy_benchmark() {
        let parser = IngredientParser::new();
        
        // Known good ingredient parsing test cases
        let benchmark_cases = vec![
            // Basic measurements
            ("1 cup flour", "flour", 1.0, "cup"),
            ("2 tablespoons butter", "butter", 2.0, "tablespoon"),
            ("3 teaspoons salt", "salt", 3.0, "teaspoon"),
            
            // Fractional measurements
            ("1/2 cup sugar", "sugar", 0.5, "cup"),
            ("1/4 teaspoon pepper", "pepper", 0.25, "teaspoon"),
            ("3/4 cup milk", "milk", 0.75, "cup"),
            
            // Mixed numbers
            ("1 1/2 cups water", "water", 1.5, "cup"),
            ("2 1/4 pounds beef", "beef", 2.25, "pound"),
            
            // Complex ingredients
            ("2 cups all-purpose flour", "flour", 2.0, "cup"),
            ("1 tablespoon extra virgin olive oil", "olive oil", 1.0, "tablespoon"),
            ("3 large eggs, beaten", "eggs", 3.0, ""),
            
            // Preparation notes
            ("1 onion, diced", "onion", 1.0, ""),
            ("2 cloves garlic, minced", "garlic", 2.0, "clove"),
            ("1 cup carrots, chopped", "carrots", 1.0, "cup"),
        ];

        let mut correct_parses = 0;
        let total_cases = benchmark_cases.len();

        for (ingredient_text, expected_name, expected_amount, expected_unit) in benchmark_cases {
            match parser.parse_ingredient(ingredient_text, None).await {
                Ok(Some(ingredient)) => {
                    let mut is_correct = true;
                    
                    // Check name accuracy (fuzzy match)
                    if !ingredient.name.to_lowercase().contains(&expected_name.to_lowercase()) {
                        println!("✗ Name mismatch for '{}': expected '{}', got '{}'", 
                            ingredient_text, expected_name, ingredient.name);
                        is_correct = false;
                    }
                    
                    // Check amount accuracy
                    if !ingredient.amount.is_empty() {
                        let actual_amount: f64 = ingredient.amount.parse().unwrap_or(0.0);
                        if (actual_amount - expected_amount).abs() > 0.1 {
                            println!("✗ Amount mismatch for '{}': expected {}, got {}",
                                ingredient_text, expected_amount, actual_amount);
                            is_correct = false;
                        }
                    } else if expected_amount > 0.0 {
                        println!("✗ Missing amount for '{}': expected {}",
                            ingredient_text, expected_amount);
                        is_correct = false;
                    }
                    
                    // Check unit accuracy (if expected)
                    if !expected_unit.is_empty() {
                        if !ingredient.unit.is_empty() {
                            let actual_unit = &ingredient.unit;
                            if !actual_unit.to_lowercase().contains(&expected_unit.to_lowercase()) {
                                println!("✗ Unit mismatch for '{}': expected '{}', got '{}'",
                                    ingredient_text, expected_unit, actual_unit);
                                is_correct = false;
                            }
                        } else {
                            println!("✗ Missing unit for '{}': expected '{}'",
                                ingredient_text, expected_unit);
                            is_correct = false;
                        }
                    }
                    
                    if is_correct {
                        correct_parses += 1;
                        println!("✓ Correctly parsed: '{}'", ingredient_text);
                    }
                }
                Ok(None) => {
                    println!("✗ Failed to parse (returned None): '{}'", ingredient_text);
                }
                Err(e) => {
                    println!("✗ Failed to parse (error): '{}' - {}", ingredient_text, e);
                }
            }
        }

        let accuracy = correct_parses as f64 / total_cases as f64;
        println!("Parsing accuracy benchmark results:");
        println!("  Correct parses: {}/{}", correct_parses, total_cases);
        println!("  Accuracy: {:.1}%", accuracy * 100.0);

        // Should achieve at least 70% accuracy on benchmark dataset
        assert!(accuracy >= 0.7, 
            "Parsing accuracy too low: {:.1}% (expected >= 70%)", accuracy * 100.0);
    }

    /// Test memory usage patterns during extended parsing
    #[tokio::test]
    async fn test_memory_usage_patterns() {
        let parser = IngredientParser::new();
        
        // Get initial metrics
        let initial_metrics = parser.get_metrics().await;
        
        // Perform multiple rounds of parsing to test memory stability
        let rounds = 5;
        let ingredients_per_round = 10;
        
        let test_ingredients = vec![
            "2 cups flour".to_string(),
            "1 tbsp oil".to_string(),
            "3 eggs".to_string(),
            "1/2 cup sugar".to_string(),
            "1 tsp vanilla".to_string(),
            "1 cup milk".to_string(),
            "2 tsp baking powder".to_string(),
            "1/2 tsp salt".to_string(),
            "1/4 cup butter".to_string(),
            "1 tsp cinnamon".to_string(),
        ];

        for round in 0..rounds {
            let start_time = Instant::now();
            let _results = parser.parse_ingredients_batch(&test_ingredients).await;
            let round_time = start_time.elapsed();
            
            let current_metrics = parser.get_metrics().await;
            
            println!("Round {} completed in {}ms", round + 1, round_time.as_millis());
            println!("  Total requests: {}", current_metrics.total_requests);
            println!("  Average latency: {:.2}ms", current_metrics.average_latency_ms);
            
            // Small delay between rounds
            tokio::time::sleep(Duration::from_millis(100)).await;
        }
        
        let final_metrics = parser.get_metrics().await;
        
        println!("Memory usage test results:");
        println!("  Initial requests: {}", initial_metrics.total_requests);
        println!("  Final requests: {}", final_metrics.total_requests);
        println!("  Total processed: {}", final_metrics.total_requests - initial_metrics.total_requests);
        println!("  Final average latency: {:.2}ms", final_metrics.average_latency_ms);
        
        // Verify metrics are being tracked correctly
        assert!(final_metrics.total_requests >= initial_metrics.total_requests + (rounds * ingredients_per_round) as u64);
        assert!(final_metrics.average_latency_ms >= 0.0);
        
        // Memory usage should remain stable (latency shouldn't increase dramatically)
        if initial_metrics.total_requests > 0 {
            let latency_increase = final_metrics.average_latency_ms / initial_metrics.average_latency_ms.max(1.0);
            assert!(latency_increase < 2.0, 
                "Memory leak suspected: latency increased by {:.1}x", latency_increase);
        }
    }
}
